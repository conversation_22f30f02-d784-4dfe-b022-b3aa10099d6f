import express from 'express';
import cors from 'cors';
import { virtualMatchService } from './services/virtualMatchService.js';
import { oddsMatchingService } from './services/oddsMatchingService.js';

const app = express();
const PORT = process.env.PORT || 5000;

// Middleware
app.use(cors());
app.use(express.json());

// Routes
app.get('/api/virtual-matches', async (req, res) => {
  try {
    const matches = await virtualMatchService.getScheduledMatches();
    res.json({ success: true, matches });
  } catch (error) {
    console.error('Error fetching virtual matches:', error);
    res.status(500).json({ success: false, error: 'Failed to fetch virtual matches' });
  }
});

app.post('/api/search', async (req, res) => {
  try {
    const { odds } = req.body;
    
    if (!odds || !Array.isArray(odds) || odds.length !== 3) {
      return res.status(400).json({ 
        success: false, 
        error: 'Please provide exactly 3 odds values [home, draw, away]' 
      });
    }

    const matches = await oddsMatchingService.findMatchingOdds(odds);
    res.json({ success: true, matches });
  } catch (error) {
    console.error('Error searching matches:', error);
    res.status(500).json({ success: false, error: 'Failed to search matches' });
  }
});

app.post('/api/refresh-matches', async (req, res) => {
  try {
    await virtualMatchService.refreshMatches();
    res.json({ success: true, message: 'Matches refreshed successfully' });
  } catch (error) {
    console.error('Error refreshing matches:', error);
    res.status(500).json({ success: false, error: 'Failed to refresh matches' });
  }
});

app.get('/api/health', (req, res) => {
  res.json({ success: true, message: 'Virtual Match Finder API is running' });
});

// Start server
app.listen(PORT, () => {
  console.log(`🚀 Virtual Match Finder API running on port ${PORT}`);
  console.log(`📊 Health check: http://localhost:${PORT}/api/health`);
  
  // Initialize virtual match service
  virtualMatchService.initialize();
});
