// File: src/App.jsx
import React, { useState } from "react";
import axios from "axios";

export default function App() {
  const [oddsInput, setOddsInput] = useState("");
  const [matches, setMatches] = useState([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);

  const handleSearch = async () => {
    const odds = oddsInput
      .split(/[ ,]+/)
      .map((o) => parseFloat(o.trim()))
      .filter((o) => !isNaN(o));

    if (odds.length !== 3) {
      setError("Please enter exactly 3 odds (Home, Draw, Away).");
      return;
    }

    setLoading(true);
    setError(null);
    try {
      const res = await axios.post("http://localhost:5000/search", { odds });
      setMatches(res.data.matches);
    } catch (err) {
      setError("Error fetching matches.");
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="min-h-screen bg-gray-900 text-white p-6">
      <h1 className="text-2xl font-bold mb-4">Virtual Match Finder</h1>
      <p className="mb-2 text-sm text-gray-400">
        Enter odds (home draw away) separated by space or comma:
      </p>
      <input
        type="text"
        value={oddsInput}
        onChange={(e) => setOddsInput(e.target.value)}
        placeholder="e.g., 3.55 2.35 1.80"
        className="p-2 rounded w-full text-black"
      />
      <button
        onClick={handleSearch}
        className="bg-green-600 px-4 py-2 mt-4 rounded hover:bg-green-700"
      >
        {loading ? "Searching..." : "Search"}
      </button>

      {error && <p className="text-red-500 mt-4">{error}</p>}

      <div className="mt-6">
        {matches.length > 0 ? (
          <div>
            <h2 className="text-lg font-semibold mb-2">Matching Fixtures:</h2>
            <ul className="space-y-2">
              {matches.map((match, index) => (
                <li
                  key={index}
                  className="bg-gray-800 p-3 rounded shadow-md border border-gray-700"
                >
                  <div>
                    <strong>{match.match}</strong>
                  </div>
                  <div>Time: {match.time}</div>
                  <div>
                    Odds: {match.odds.home} - {match.odds.draw} -{" "}
                    {match.odds.away}
                  </div>
                </li>
              ))}
            </ul>
          </div>
        ) : (
          <p className="text-sm text-gray-400">
            No matches found or not searched yet.
          </p>
        )}
      </div>
    </div>
  );
}
