// File: src/App.jsx
import React, { useState, useEffect } from "react";
import axios from "axios";

export default function App() {
  const [oddsInput, setOddsInput] = useState("");
  const [matches, setMatches] = useState([]);
  const [allMatches, setAllMatches] = useState([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [tolerance, setTolerance] = useState(5);
  const [stats, setStats] = useState(null);

  // Load all available matches on component mount
  useEffect(() => {
    loadAllMatches();
  }, []);

  const loadAllMatches = async () => {
    try {
      const res = await axios.get("http://localhost:5001/api/virtual-matches");
      setAllMatches(res.data.matches || []);
    } catch (err) {
      console.error("Error loading matches:", err);
    }
  };

  const handleSearch = async () => {
    const odds = oddsInput
      .split(/[ ,]+/)
      .map((o) => parseFloat(o.trim()))
      .filter((o) => !isNaN(o));

    if (odds.length !== 3) {
      setError("Please enter exactly 3 odds (Home, Draw, Away).");
      return;
    }

    setLoading(true);
    setError(null);
    try {
      const res = await axios.post("http://localhost:5001/api/search", {
        odds,
        tolerance: tolerance / 100, // Convert percentage to decimal
      });
      setMatches(res.data.matches || []);

      // Calculate and set statistics
      if (res.data.matches) {
        const exactMatches = res.data.matches.filter(
          (m) => m.similarity > 0.99
        ).length;
        const closeMatches = res.data.matches.filter(
          (m) => m.similarity > 0.9
        ).length;
        const avgSimilarity =
          res.data.matches.reduce((sum, m) => sum + (m.similarity || 0), 0) /
          res.data.matches.length;

        setStats({
          total: res.data.matches.length,
          exact: exactMatches,
          close: closeMatches,
          avgSimilarity: avgSimilarity || 0,
        });
      }
    } catch (err) {
      setError(
        "Error fetching matches. Make sure the backend server is running on port 5001."
      );
      console.error("Search error:", err);
    } finally {
      setLoading(false);
    }
  };

  const refreshMatches = async () => {
    try {
      setLoading(true);
      await axios.post("http://localhost:5001/api/refresh-matches");
      await loadAllMatches();
      setError(null);
    } catch (err) {
      setError("Error refreshing matches.");
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="min-h-screen bg-gray-900 text-white p-6">
      <div className="max-w-6xl mx-auto">
        <h1 className="text-4xl font-bold mb-6 text-blue-500 text-center">
          🎯 Virtual Match Finder
        </h1>
        <p className="mb-4 text-center text-gray-400">
          Find virtual football matches that match your SportyBet odds
        </p>

        {/* Search Section */}
        <div className="bg-gray-800 p-6 rounded-lg mb-6">
          <h2 className="text-xl font-semibold mb-4">Search by Odds</h2>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
            <div>
              <label className="block text-sm text-gray-400 mb-2">
                Enter odds (Home, Draw, Away):
              </label>
              <input
                type="text"
                value={oddsInput}
                onChange={(e) => setOddsInput(e.target.value)}
                placeholder="e.g., 3.55 2.35 1.80"
                className="p-3 rounded w-full text-black text-lg"
              />
            </div>

            <div>
              <label className="block text-sm text-gray-400 mb-2">
                Tolerance ({tolerance}%):
              </label>
              <input
                type="range"
                min="1"
                max="20"
                value={tolerance}
                onChange={(e) => setTolerance(e.target.value)}
                className="w-full"
              />
              <div className="flex justify-between text-xs text-gray-500 mt-1">
                <span>1%</span>
                <span>20%</span>
              </div>
            </div>
          </div>

          <div className="flex gap-3">
            <button
              onClick={handleSearch}
              disabled={loading}
              className="bg-green-600 px-6 py-3 rounded hover:bg-green-700 disabled:opacity-50 flex-1"
            >
              {loading ? "🔍 Searching..." : "🔍 Find Matches"}
            </button>

            <button
              onClick={refreshMatches}
              disabled={loading}
              className="bg-blue-600 px-6 py-3 rounded hover:bg-blue-700 disabled:opacity-50"
            >
              {loading ? "⟳" : "🔄 Refresh"}
            </button>
          </div>
        </div>

        {/* Error Display */}
        {error && (
          <div className="bg-red-900 border border-red-700 p-4 rounded mb-6">
            <p className="text-red-300">❌ {error}</p>
          </div>
        )}

        {/* Statistics */}
        {stats && (
          <div className="bg-gray-800 p-4 rounded-lg mb-6">
            <h3 className="text-lg font-semibold mb-3">📊 Search Results</h3>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-center">
              <div className="bg-gray-700 p-3 rounded">
                <div className="text-2xl font-bold text-blue-400">
                  {stats.total}
                </div>
                <div className="text-sm text-gray-400">Total Matches</div>
              </div>
              <div className="bg-gray-700 p-3 rounded">
                <div className="text-2xl font-bold text-green-400">
                  {stats.exact}
                </div>
                <div className="text-sm text-gray-400">Exact Matches</div>
              </div>
              <div className="bg-gray-700 p-3 rounded">
                <div className="text-2xl font-bold text-yellow-400">
                  {stats.close}
                </div>
                <div className="text-sm text-gray-400">Close Matches</div>
              </div>
              <div className="bg-gray-700 p-3 rounded">
                <div className="text-2xl font-bold text-purple-400">
                  {(stats.avgSimilarity * 100).toFixed(1)}%
                </div>
                <div className="text-sm text-gray-400">Avg Similarity</div>
              </div>
            </div>
          </div>
        )}

        {/* Results */}
        <div className="bg-gray-800 p-6 rounded-lg">
          {matches.length > 0 ? (
            <div>
              <h2 className="text-xl font-semibold mb-4">
                🎮 Matching Virtual Fixtures ({matches.length})
              </h2>
              <div className="grid gap-4">
                {matches.map((match, index) => (
                  <div
                    key={match.id || index}
                    className="bg-gray-700 p-4 rounded-lg border border-gray-600 hover:border-gray-500 transition-colors"
                  >
                    <div className="flex justify-between items-start mb-2">
                      <div>
                        <h3 className="text-lg font-bold text-white">
                          {match.match}
                        </h3>
                        <p className="text-sm text-gray-400">{match.league}</p>
                      </div>
                      <div className="text-right">
                        <div className="text-sm text-gray-400">
                          ⏰{" "}
                          {match.displayTime ||
                            new Date(match.time).toLocaleTimeString()}
                        </div>
                        {match.similarity && (
                          <div className="text-sm font-semibold text-green-400">
                            {(match.similarity * 100).toFixed(1)}% match
                          </div>
                        )}
                      </div>
                    </div>

                    <div className="grid grid-cols-3 gap-4 mt-3">
                      <div className="text-center bg-gray-600 p-2 rounded">
                        <div className="text-xs text-gray-400">Home</div>
                        <div className="text-lg font-bold">
                          {match.odds.home}
                        </div>
                      </div>
                      <div className="text-center bg-gray-600 p-2 rounded">
                        <div className="text-xs text-gray-400">Draw</div>
                        <div className="text-lg font-bold">
                          {match.odds.draw}
                        </div>
                      </div>
                      <div className="text-center bg-gray-600 p-2 rounded">
                        <div className="text-xs text-gray-400">Away</div>
                        <div className="text-lg font-bold">
                          {match.odds.away}
                        </div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          ) : (
            <div className="text-center py-12">
              <div className="text-6xl mb-4">⚽</div>
              <h3 className="text-xl font-semibold mb-2">No matches found</h3>
              <p className="text-gray-400 mb-4">
                {allMatches.length > 0
                  ? "Try adjusting your odds or increasing the tolerance."
                  : "Loading virtual matches... Please wait or refresh."}
              </p>
              {allMatches.length > 0 && (
                <p className="text-sm text-gray-500">
                  {allMatches.length} virtual matches available
                </p>
              )}
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
