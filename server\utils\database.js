import sqlite3 from 'sqlite3';
import { promisify } from 'util';

class Database {
  constructor() {
    this.db = null;
    this.isInitialized = false;
  }

  async initialize() {
    try {
      console.log('🔄 Initializing database...');
      
      this.db = new sqlite3.Database('./virtual_matches.db');
      
      // Promisify database methods
      this.db.run = promisify(this.db.run.bind(this.db));
      this.db.get = promisify(this.db.get.bind(this.db));
      this.db.all = promisify(this.db.all.bind(this.db));
      
      await this.createTables();
      
      this.isInitialized = true;
      console.log('✅ Database initialized successfully');
    } catch (error) {
      console.error('❌ Failed to initialize database:', error);
      throw error;
    }
  }

  async createTables() {
    const createMatchesTable = `
      CREATE TABLE IF NOT EXISTS virtual_matches (
        id TEXT PRIMARY KEY,
        home_team TEXT NOT NULL,
        away_team TEXT NOT NULL,
        match_name TEXT NOT NULL,
        match_time TEXT NOT NULL,
        display_time TEXT NOT NULL,
        home_odds REAL NOT NULL,
        draw_odds REAL NOT NULL,
        away_odds REAL NOT NULL,
        league TEXT NOT NULL,
        status TEXT DEFAULT 'scheduled',
        created_at TEXT NOT NULL
      )
    `;

    const createIndexes = `
      CREATE INDEX IF NOT EXISTS idx_match_time ON virtual_matches(match_time);
      CREATE INDEX IF NOT EXISTS idx_status ON virtual_matches(status);
      CREATE INDEX IF NOT EXISTS idx_odds ON virtual_matches(home_odds, draw_odds, away_odds);
    `;

    await this.db.run(createMatchesTable);
    await this.db.run(createIndexes);
  }

  async saveMatches(matches) {
    if (!this.isInitialized) {
      await this.initialize();
    }

    const insertQuery = `
      INSERT OR REPLACE INTO virtual_matches 
      (id, home_team, away_team, match_name, match_time, display_time, 
       home_odds, draw_odds, away_odds, league, status, created_at)
      VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    `;

    try {
      for (const match of matches) {
        await this.db.run(insertQuery, [
          match.id,
          match.homeTeam,
          match.awayTeam,
          match.match,
          match.time,
          match.displayTime,
          match.odds.home,
          match.odds.draw,
          match.odds.away,
          match.league,
          match.status,
          match.createdAt
        ]);
      }
    } catch (error) {
      console.error('Error saving matches to database:', error);
      throw error;
    }
  }

  async getActiveMatches() {
    if (!this.isInitialized) {
      await this.initialize();
    }

    const query = `
      SELECT * FROM virtual_matches 
      WHERE status = 'scheduled' AND match_time > datetime('now')
      ORDER BY match_time ASC
    `;

    try {
      const rows = await this.db.all(query);
      return rows.map(this.rowToMatch);
    } catch (error) {
      console.error('Error getting active matches:', error);
      return [];
    }
  }

  async getMatchesByOdds(homeOdds, drawOdds, awayOdds, tolerance = 0.05) {
    if (!this.isInitialized) {
      await this.initialize();
    }

    const query = `
      SELECT * FROM virtual_matches 
      WHERE status = 'scheduled' 
        AND match_time > datetime('now')
        AND ABS(home_odds - ?) / ((home_odds + ?) / 2) <= ?
        AND ABS(draw_odds - ?) / ((draw_odds + ?) / 2) <= ?
        AND ABS(away_odds - ?) / ((away_odds + ?) / 2) <= ?
      ORDER BY match_time ASC
    `;

    try {
      const rows = await this.db.all(query, [
        homeOdds, homeOdds, tolerance,
        drawOdds, drawOdds, tolerance,
        awayOdds, awayOdds, tolerance
      ]);
      return rows.map(this.rowToMatch);
    } catch (error) {
      console.error('Error getting matches by odds:', error);
      return [];
    }
  }

  async cleanupOldMatches(cutoffTime) {
    if (!this.isInitialized) {
      await this.initialize();
    }

    const query = `
      DELETE FROM virtual_matches 
      WHERE match_time < ?
    `;

    try {
      await this.db.run(query, [cutoffTime.toISOString()]);
    } catch (error) {
      console.error('Error cleaning up old matches:', error);
    }
  }

  rowToMatch(row) {
    return {
      id: row.id,
      homeTeam: row.home_team,
      awayTeam: row.away_team,
      match: row.match_name,
      time: row.match_time,
      displayTime: row.display_time,
      odds: {
        home: row.home_odds,
        draw: row.draw_odds,
        away: row.away_odds
      },
      league: row.league,
      status: row.status,
      createdAt: row.created_at
    };
  }

  async close() {
    if (this.db) {
      this.db.close();
      this.db = null;
      this.isInitialized = false;
    }
  }
}

export const database = new Database();
