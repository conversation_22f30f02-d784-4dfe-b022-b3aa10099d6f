import axios from 'axios';
import cron from 'node-cron';
import { teamGenerator } from '../utils/teamGenerator.js';
import { database } from '../utils/database.js';

class VirtualMatchService {
  constructor() {
    this.matches = [];
    this.isInitialized = false;
  }

  async initialize() {
    try {
      console.log('🔄 Initializing Virtual Match Service...');
      
      // Initialize database
      await database.initialize();
      
      // Load existing matches from database
      await this.loadMatchesFromDatabase();
      
      // If no matches exist, generate initial set
      if (this.matches.length === 0) {
        await this.generateInitialMatches();
      }
      
      // Schedule automatic match generation every 15 minutes
      this.scheduleMatchGeneration();
      
      this.isInitialized = true;
      console.log('✅ Virtual Match Service initialized successfully');
      console.log(`📊 Loaded ${this.matches.length} virtual matches`);
    } catch (error) {
      console.error('❌ Failed to initialize Virtual Match Service:', error);
    }
  }

  async loadMatchesFromDatabase() {
    try {
      this.matches = await database.getActiveMatches();
    } catch (error) {
      console.error('Error loading matches from database:', error);
      this.matches = [];
    }
  }

  async generateInitialMatches() {
    console.log('🎲 Generating initial virtual matches...');
    
    const matchCount = 50; // Generate 50 initial matches
    const newMatches = [];
    
    for (let i = 0; i < matchCount; i++) {
      const match = this.generateVirtualMatch(i);
      newMatches.push(match);
    }
    
    // Save to database
    await database.saveMatches(newMatches);
    this.matches = newMatches;
    
    console.log(`✅ Generated ${matchCount} initial virtual matches`);
  }

  generateVirtualMatch(index = 0) {
    const teams = teamGenerator.generateMatchup();
    const odds = this.generateRealisticOdds();
    const matchTime = this.generateMatchTime(index);
    
    return {
      id: `vm_${Date.now()}_${index}`,
      homeTeam: teams.home,
      awayTeam: teams.away,
      match: `${teams.home} vs ${teams.away}`,
      time: matchTime.toISOString(),
      displayTime: this.formatMatchTime(matchTime),
      odds: {
        home: odds.home,
        draw: odds.draw,
        away: odds.away
      },
      league: teams.league,
      status: 'scheduled',
      createdAt: new Date().toISOString()
    };
  }

  generateRealisticOdds() {
    // Generate realistic football odds
    const scenarios = [
      // Balanced match
      { home: 2.20 + Math.random() * 0.4, draw: 3.10 + Math.random() * 0.3, away: 2.80 + Math.random() * 0.4 },
      // Home favorite
      { home: 1.50 + Math.random() * 0.3, draw: 3.80 + Math.random() * 0.4, away: 4.50 + Math.random() * 1.0 },
      // Away favorite  
      { home: 4.20 + Math.random() * 1.0, draw: 3.60 + Math.random() * 0.4, away: 1.60 + Math.random() * 0.3 },
      // Draw likely
      { home: 2.90 + Math.random() * 0.5, draw: 2.80 + Math.random() * 0.3, away: 2.70 + Math.random() * 0.5 }
    ];
    
    const scenario = scenarios[Math.floor(Math.random() * scenarios.length)];
    
    return {
      home: parseFloat(scenario.home.toFixed(2)),
      draw: parseFloat(scenario.draw.toFixed(2)),
      away: parseFloat(scenario.away.toFixed(2))
    };
  }

  generateMatchTime(index) {
    const now = new Date();
    const baseMinutes = 5 + (index * 3); // Matches every 3 minutes starting from 5 minutes
    return new Date(now.getTime() + baseMinutes * 60000);
  }

  formatMatchTime(date) {
    return date.toLocaleTimeString('en-US', { 
      hour: '2-digit', 
      minute: '2-digit',
      hour12: true 
    });
  }

  scheduleMatchGeneration() {
    // Generate new matches every 15 minutes
    cron.schedule('*/15 * * * *', async () => {
      console.log('🔄 Generating new virtual matches...');
      await this.generateNewMatches();
    });
    
    // Clean up old matches every hour
    cron.schedule('0 * * * *', async () => {
      console.log('🧹 Cleaning up old matches...');
      await this.cleanupOldMatches();
    });
  }

  async generateNewMatches() {
    try {
      const newMatchCount = 10; // Generate 10 new matches
      const newMatches = [];
      
      for (let i = 0; i < newMatchCount; i++) {
        const match = this.generateVirtualMatch(this.matches.length + i);
        newMatches.push(match);
      }
      
      // Save to database
      await database.saveMatches(newMatches);
      
      // Add to memory
      this.matches.push(...newMatches);
      
      console.log(`✅ Generated ${newMatchCount} new virtual matches`);
    } catch (error) {
      console.error('Error generating new matches:', error);
    }
  }

  async cleanupOldMatches() {
    try {
      const cutoffTime = new Date(Date.now() - 2 * 60 * 60 * 1000); // 2 hours ago
      
      // Remove from memory
      const initialCount = this.matches.length;
      this.matches = this.matches.filter(match => new Date(match.time) > cutoffTime);
      
      // Remove from database
      await database.cleanupOldMatches(cutoffTime);
      
      const removedCount = initialCount - this.matches.length;
      if (removedCount > 0) {
        console.log(`🗑️ Cleaned up ${removedCount} old matches`);
      }
    } catch (error) {
      console.error('Error cleaning up old matches:', error);
    }
  }

  async getScheduledMatches() {
    if (!this.isInitialized) {
      await this.initialize();
    }
    
    // Return matches scheduled for the next 2 hours
    const now = new Date();
    const twoHoursFromNow = new Date(now.getTime() + 2 * 60 * 60 * 1000);
    
    return this.matches
      .filter(match => {
        const matchTime = new Date(match.time);
        return matchTime > now && matchTime <= twoHoursFromNow;
      })
      .sort((a, b) => new Date(a.time) - new Date(b.time));
  }

  async refreshMatches() {
    console.log('🔄 Refreshing virtual matches...');
    await this.generateNewMatches();
    return this.getScheduledMatches();
  }
}

export const virtualMatchService = new VirtualMatchService();
