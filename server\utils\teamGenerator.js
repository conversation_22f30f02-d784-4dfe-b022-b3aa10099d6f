class TeamGenerator {
  constructor() {
    this.teams = {
      premier: [
        'Arsenal', 'Chelsea', 'Liverpool', 'Manchester City', 'Manchester United',
        'Tottenham', 'Newcastle', 'Brighton', 'Aston Villa', 'West Ham',
        'Crystal Palace', 'Fulham', 'Wolves', 'Everton', 'Brentford',
        'Nottingham Forest', 'Bournemouth', 'Sheffield United', 'Burnley', 'Luton Town'
      ],
      laliga: [
        'Real Madrid', 'Barcelona', 'Atletico Madrid', 'Sevilla', 'Real Sociedad',
        'Villarreal', 'Real Betis', 'Valencia', 'Athletic Bilbao', 'Osasuna',
        'Getafe', 'Las Palmas', 'Girona', 'Mallorca', 'Cadiz',
        'Celta Vigo', 'Rayo Vallecano', 'Alaves', 'Granada', 'Almeria'
      ],
      bundesliga: [
        'Bayern Munich', 'Borussia Dortmund', 'RB Leipzig', 'Union Berlin', 'SC Freiburg',
        'Bayer Leverkusen', 'Eintracht Frankfurt', 'Wolfsburg', 'Mainz', 'Borussia Monchengladbach',
        'FC Koln', 'Hoffenheim', 'Werder Bremen', 'Augsburg', 'Heidenheim',
        'VfL Bochum', 'Stuttgart', 'Darmstadt', 'Hamburg', 'Hannover'
      ],
      seriea: [
        'Juventus', 'AC Milan', 'Inter Milan', 'Napoli', 'AS Roma',
        'Lazio', 'Atalanta', 'Fiorentina', 'Bologna', 'Torino',
        'Genoa', 'Monza', 'Verona', 'Udinese', 'Cagliari',
        'Lecce', 'Frosinone', 'Empoli', 'Sassuolo', 'Salernitana'
      ],
      ligue1: [
        'Paris Saint-Germain', 'Monaco', 'Lille', 'Rennes', 'Nice',
        'Lyon', 'Marseille', 'Lens', 'Strasbourg', 'Nantes',
        'Montpellier', 'Brest', 'Reims', 'Toulouse', 'Le Havre',
        'Metz', 'Lorient', 'Clermont', 'Ajaccio', 'Angers'
      ],
      virtual: [
        'FC Thunder', 'Lightning United', 'Storm City', 'Blaze FC', 'Phoenix Rising',
        'Dragon Warriors', 'Eagle Soar', 'Lion Pride', 'Tiger Strike', 'Wolf Pack',
        'Falcon Flight', 'Hawk Eye', 'Panther Power', 'Cobra Strike', 'Viper Venom',
        'Shark Attack', 'Rhino Charge', 'Bull Force', 'Bear Strength', 'Leopard Speed'
      ]
    };

    this.leagues = [
      { name: 'Premier League', key: 'premier', weight: 25 },
      { name: 'La Liga', key: 'laliga', weight: 25 },
      { name: 'Bundesliga', key: 'bundesliga', weight: 20 },
      { name: 'Serie A', key: 'seriea', weight: 20 },
      { name: 'Ligue 1', key: 'ligue1', weight: 15 },
      { name: 'Virtual League', key: 'virtual', weight: 30 }
    ];
  }

  generateMatchup() {
    // Select league based on weights
    const league = this.selectWeightedLeague();
    const leagueTeams = this.teams[league.key];
    
    // Select two different teams
    const homeIndex = Math.floor(Math.random() * leagueTeams.length);
    let awayIndex = Math.floor(Math.random() * leagueTeams.length);
    
    // Ensure different teams
    while (awayIndex === homeIndex) {
      awayIndex = Math.floor(Math.random() * leagueTeams.length);
    }
    
    return {
      home: leagueTeams[homeIndex],
      away: leagueTeams[awayIndex],
      league: league.name
    };
  }

  selectWeightedLeague() {
    const totalWeight = this.leagues.reduce((sum, league) => sum + league.weight, 0);
    let random = Math.random() * totalWeight;
    
    for (const league of this.leagues) {
      random -= league.weight;
      if (random <= 0) {
        return league;
      }
    }
    
    // Fallback
    return this.leagues[0];
  }

  getRandomTeam(leagueKey = null) {
    if (leagueKey && this.teams[leagueKey]) {
      const teams = this.teams[leagueKey];
      return teams[Math.floor(Math.random() * teams.length)];
    }
    
    // Random from all teams
    const allTeams = Object.values(this.teams).flat();
    return allTeams[Math.floor(Math.random() * allTeams.length)];
  }

  getAllTeams() {
    return this.teams;
  }

  getLeagues() {
    return this.leagues;
  }
}

export const teamGenerator = new TeamGenerator();
