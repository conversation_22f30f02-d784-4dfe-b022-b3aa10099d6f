import { virtualMatchService } from './virtualMatchService.js';

class OddsMatchingService {
  constructor() {
    this.defaultTolerance = 0.05; // 5% tolerance by default
  }

  async findMatchingOdds(inputOdds, tolerance = this.defaultTolerance) {
    try {
      const [homeOdds, drawOdds, awayOdds] = inputOdds;
      
      // Get all scheduled matches
      const allMatches = await virtualMatchService.getScheduledMatches();
      
      // Find matches with similar odds
      const matchingMatches = allMatches.filter(match => {
        return this.isOddsMatch(
          [homeOdds, drawOdds, awayOdds],
          [match.odds.home, match.odds.draw, match.odds.away],
          tolerance
        );
      });

      // Sort by closest match first
      const sortedMatches = matchingMatches
        .map(match => ({
          ...match,
          similarity: this.calculateOddsSimilarity(
            [homeOdds, drawOdds, awayOdds],
            [match.odds.home, match.odds.draw, match.odds.away]
          )
        }))
        .sort((a, b) => b.similarity - a.similarity);

      return sortedMatches;
    } catch (error) {
      console.error('Error finding matching odds:', error);
      throw error;
    }
  }

  isOddsMatch(inputOdds, matchOdds, tolerance) {
    const [inputHome, inputDraw, inputAway] = inputOdds;
    const [matchHome, matchDraw, matchAway] = matchOdds;

    // Check if each odds value is within tolerance
    const homeMatch = this.isWithinTolerance(inputHome, matchHome, tolerance);
    const drawMatch = this.isWithinTolerance(inputDraw, matchDraw, tolerance);
    const awayMatch = this.isWithinTolerance(inputAway, matchAway, tolerance);

    // All three odds must match within tolerance
    return homeMatch && drawMatch && awayMatch;
  }

  isWithinTolerance(value1, value2, tolerance) {
    const difference = Math.abs(value1 - value2);
    const average = (value1 + value2) / 2;
    const percentageDifference = difference / average;
    
    return percentageDifference <= tolerance;
  }

  calculateOddsSimilarity(inputOdds, matchOdds) {
    const [inputHome, inputDraw, inputAway] = inputOdds;
    const [matchHome, matchDraw, matchAway] = matchOdds;

    // Calculate percentage differences
    const homeDiff = Math.abs(inputHome - matchHome) / ((inputHome + matchHome) / 2);
    const drawDiff = Math.abs(inputDraw - matchDraw) / ((inputDraw + matchDraw) / 2);
    const awayDiff = Math.abs(inputAway - matchAway) / ((inputAway + matchAway) / 2);

    // Average difference (lower is better)
    const avgDifference = (homeDiff + drawDiff + awayDiff) / 3;
    
    // Convert to similarity score (higher is better)
    return Math.max(0, 1 - avgDifference);
  }

  async findExactMatches(inputOdds) {
    return this.findMatchingOdds(inputOdds, 0.001); // 0.1% tolerance for "exact" matches
  }

  async findCloseMatches(inputOdds) {
    return this.findMatchingOdds(inputOdds, 0.10); // 10% tolerance for close matches
  }

  async findMatchesWithCustomTolerance(inputOdds, customTolerance) {
    return this.findMatchingOdds(inputOdds, customTolerance);
  }

  // Advanced matching with weighted importance
  async findWeightedMatches(inputOdds, weights = { home: 1, draw: 1, away: 1 }) {
    try {
      const [homeOdds, drawOdds, awayOdds] = inputOdds;
      const allMatches = await virtualMatchService.getScheduledMatches();
      
      const matchingMatches = allMatches.map(match => {
        const homeDiff = Math.abs(homeOdds - match.odds.home) / homeOdds;
        const drawDiff = Math.abs(drawOdds - match.odds.draw) / drawOdds;
        const awayDiff = Math.abs(awayOdds - match.odds.away) / awayOdds;
        
        // Weighted score
        const weightedScore = (
          (homeDiff * weights.home) +
          (drawDiff * weights.draw) +
          (awayDiff * weights.away)
        ) / (weights.home + weights.draw + weights.away);
        
        return {
          ...match,
          weightedScore,
          similarity: Math.max(0, 1 - weightedScore)
        };
      });

      // Return top matches sorted by weighted score
      return matchingMatches
        .sort((a, b) => a.weightedScore - b.weightedScore)
        .slice(0, 20); // Return top 20 matches
    } catch (error) {
      console.error('Error finding weighted matches:', error);
      throw error;
    }
  }

  // Get match statistics
  getMatchingStats(inputOdds, matches) {
    if (!matches || matches.length === 0) {
      return {
        totalMatches: 0,
        exactMatches: 0,
        closeMatches: 0,
        averageSimilarity: 0
      };
    }

    const exactMatches = matches.filter(m => m.similarity > 0.99).length;
    const closeMatches = matches.filter(m => m.similarity > 0.90).length;
    const averageSimilarity = matches.reduce((sum, m) => sum + (m.similarity || 0), 0) / matches.length;

    return {
      totalMatches: matches.length,
      exactMatches,
      closeMatches,
      averageSimilarity: parseFloat(averageSimilarity.toFixed(3))
    };
  }
}

export const oddsMatchingService = new OddsMatchingService();
